import React from 'react';
import { Mail } from 'lucide-react';
import Typography from '../atoms/Typography';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Platform",
      links: [
        { name: "SymbioCore", href: "/symbiocore", description: "Core AI Infrastructure" },
        { name: "SymbioLabs", href: "/symbiolabs", description: "Research & Development" },
        { name: "SymbioXchange", href: "/symbioxchange", description: "AI Marketplace" },
        { name: "SymbioAutomate", href: "/symbioautomate", description: "Process Automation" },
      ]
    },
    {
      title: "Solutions",
      links: [
        { name: "SymbioEdge", href: "/symbioedge", description: "Edge Computing" },
        { name: "SymbioImpact", href: "/symbioimpact", description: "ESG & Impact" },
        { name: "SymbioVentures", href: "/symbioventures", description: "Investment Platform" },
        { name: "SymbioAlliance", href: "/symbioalliance", description: "Partner Network" },
      ]
    },
    {
      title: "Developers",
      links: [
        { name: "API Documentation", href: "/documentation", description: "Technical Guides" },
        { name: "SDK & Tools", href: "/sdk", description: "Development Kit" },
        { name: "Case Studies", href: "/case-studies", description: "Success Stories" },
        { name: "ACI Technology", href: "/aci-technology", description: "Core Technology" },
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About SymbioWave", href: "/about-us", description: "Our Story" },
        { name: "Leadership Team", href: "/leadership", description: "Executive Team" },
        { name: "Careers", href: "/careers", description: "Join Our Mission" },
        { name: "Investor Relations", href: "/investors", description: "Financial Info" },
      ]
    }
  ];



  return (
    <footer className="relative overflow-hidden bg-gradient-to-b from-abyssal-base via-abyssal-deep to-abyssal-void">
      {/* Clean Background */}
      <div className="absolute inset-0 opacity-20">
        {/* Simple gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-consciousness/2 via-transparent to-harmony/3"></div>
      </div>

      {/* Premium border gradient - Enhanced */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>
      <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-consciousness/20 to-transparent blur-sm"></div>

      <div className="container mx-auto px-8 py-24 relative z-10">

        {/* Enhanced Main Footer Content */}
        <div className="grid lg:grid-cols-6 gap-12 mb-16">
          {/* Premium Brand Section */}
          <div className="lg:col-span-2">
            {/* Enhanced logo section with better visual hierarchy */}
            <div className="flex items-center space-x-4 mb-8 group">
              <div className="relative">
                <div className="absolute inset-0 bg-consciousness/25 rounded-full blur-lg animate-pulse"></div>
                <img
                  src="/lovable-uploads/69b2f8ba-ee9a-4a51-954a-923e57771f4d.png"
                  alt="SymbioWave"
                  className="w-16 h-16 drop-shadow-2xl relative z-10 group-hover:scale-105 transition-transform duration-500"
                />
              </div>
              <div>
                <Typography
                  variant="2xl"
                  weight="bold"
                  font="display"
                  className="mb-1 bg-gradient-to-r from-consciousness via-white to-harmony bg-clip-text text-transparent"
                >
                  SymbioWave
                </Typography>
                <Typography variant="xs" color="consciousness" className="uppercase tracking-widest font-semibold">
                  AI Intelligence Platform
                </Typography>
              </div>
            </div>

            {/* Enhanced description with better typography */}
            <Typography
              variant="base"
              color="secondary"
              className="mb-8 leading-relaxed text-white/80"
            >
              Leading the transformation of global commerce through adaptive,
              self-optimizing AI ecosystems. Trusted by Fortune 500 companies
              to deliver measurable business outcomes.
            </Typography>

            {/* Enhanced Contact Information */}
            <div className="mb-8">
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-4 flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                Enterprise Contact
              </Typography>
              <div className="space-y-3">
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness group-hover:shadow-lg group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness group-hover:shadow-lg group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness group-hover:shadow-lg group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
              </div>
            </div>


          </div>

          {/* Premium Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-6">
              <Typography
                variant="base"
                weight="semibold"
                className="pb-3 border-b border-consciousness/25 bg-gradient-to-r from-consciousness to-harmony bg-clip-text text-transparent relative"
              >
                {section.title}
                <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-consciousness to-harmony rounded-full"></div>
              </Typography>
              <ul className="space-y-4">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="group block transition-all duration-300 hover:translate-x-2 p-2 -m-2 rounded-lg hover:bg-consciousness/5"
                    >
                      <Typography
                        variant="sm"
                        className="group-hover:text-consciousness transition-colors font-medium text-white/80 group-hover:font-semibold"
                      >
                        {link.name}
                      </Typography>
                      <Typography
                        variant="xs"
                        className="mt-1 group-hover:text-white/70 transition-colors text-white/50 leading-relaxed"
                      >
                        {link.description}
                      </Typography>
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>



        {/* Simple Bottom Bar */}
        <div className="border-t border-consciousness/25 pt-8 relative">
          {/* Subtle glow effect */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>

          <div className="text-center">
            <Typography variant="sm" className="text-white/60 font-mono">
              © {currentYear} SymbioWave Corporation. All rights reserved.
            </Typography>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;