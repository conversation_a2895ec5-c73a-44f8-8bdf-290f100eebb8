import React from 'react';
import { ArrowRight, Globe, Shield, Award, Zap, Mail, MapPin } from 'lucide-react';
import Typography from '../atoms/Typography';
import Button from '../atoms/Button';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Platform",
      links: [
        { name: "SymbioCore", href: "/symbiocore", description: "Core AI Infrastructure" },
        { name: "SymbioLabs", href: "/symbiolabs", description: "Research & Development" },
        { name: "SymbioXchange", href: "/symbioxchange", description: "AI Marketplace" },
        { name: "SymbioAutomate", href: "/symbioautomate", description: "Process Automation" },
      ]
    },
    {
      title: "Solutions",
      links: [
        { name: "SymbioEdge", href: "/symbioedge", description: "Edge Computing" },
        { name: "SymbioImpact", href: "/symbioimpact", description: "ESG & Impact" },
        { name: "SymbioVentures", href: "/symbioventures", description: "Investment Platform" },
        { name: "SymbioAlliance", href: "/symbioalliance", description: "Partner Network" },
      ]
    },
    {
      title: "Developers",
      links: [
        { name: "API Documentation", href: "/documentation", description: "Technical Guides" },
        { name: "SDK & Tools", href: "/sdk", description: "Development Kit" },
        { name: "Case Studies", href: "/case-studies", description: "Success Stories" },
        { name: "ACI Technology", href: "/aci-technology", description: "Core Technology" },
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About SymbioWave", href: "/about-us", description: "Our Story" },
        { name: "Leadership Team", href: "/leadership", description: "Executive Team" },
        { name: "Careers", href: "/careers", description: "Join Our Mission" },
        { name: "Investor Relations", href: "/investors", description: "Financial Info" },
      ]
    }
  ];

  const certifications = [
    { icon: <Shield className="w-5 h-5" />, text: "SOC 2 Type II", subtext: "Security Certified" },
    { icon: <Award className="w-5 h-5" />, text: "ISO 27001", subtext: "Information Security" },
    { icon: <Globe className="w-5 h-5" />, text: "GDPR Compliant", subtext: "Data Protection" },
    { icon: <Zap className="w-5 h-5" />, text: "Carbon Neutral", subtext: "Environmental Impact" }
  ];

  return (
    <footer className="relative overflow-hidden bg-gradient-to-b from-abyssal-base via-abyssal-deep to-abyssal-void">
      {/* Enhanced Background Pattern - Consistent with site design */}
      <div className="absolute inset-0 opacity-40">
        {/* Primary consciousness gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-consciousness/4 via-transparent to-harmony/6"></div>

        {/* Animated bio-quantum elements */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-consciousness/12 rounded-cellular blur-3xl animate-consciousness-wave"></div>
          <div className="absolute bottom-1/3 right-1/3 w-64 h-64 bg-harmony/8 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
          <div className="absolute top-1/2 right-1/4 w-32 h-32 bg-creativity/6 rounded-organic blur-xl animate-quantum-flicker"></div>
          <div className="absolute bottom-1/4 left-1/3 w-48 h-48 bg-consciousness/6 rounded-quantum blur-2xl animate-pulse"></div>
        </div>

        {/* Sophisticated grid pattern matching site design */}
        <div className="absolute inset-0 opacity-15" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(0, 229, 255, 0.2) 1px, transparent 0)`,
          backgroundSize: '64px 64px'
        }}></div>

        {/* Neural network pattern overlay */}
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: `linear-gradient(90deg, transparent 24%, rgba(0, 229, 255, 0.1) 25%, rgba(0, 229, 255, 0.1) 26%, transparent 27%, transparent 74%, rgba(0, 229, 255, 0.1) 75%, rgba(0, 229, 255, 0.1) 76%, transparent 77%, transparent), linear-gradient(transparent 24%, rgba(0, 229, 255, 0.1) 25%, rgba(0, 229, 255, 0.1) 26%, transparent 27%, transparent 74%, rgba(0, 229, 255, 0.1) 75%, rgba(0, 229, 255, 0.1) 76%, transparent 77%, transparent)`,
          backgroundSize: '128px 128px'
        }}></div>
      </div>

      {/* Premium border gradient - Enhanced */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>
      <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-consciousness/20 to-transparent blur-sm"></div>

      <div className="container mx-auto px-8 py-24 relative z-10">
        {/* Enhanced Strategic CTA Section */}
        <div className="mb-20 text-center relative">
          {/* Subtle background enhancement */}
          <div className="absolute inset-0 rounded-[40px] bg-gradient-to-br from-consciousness/5 via-transparent to-harmony/5 blur-3xl"></div>

          <div className="max-w-4xl mx-auto relative z-10">
            {/* Icon accent */}
            <div className="w-16 h-16 mx-auto mb-8 rounded-quantum bg-gradient-to-br from-consciousness/20 to-harmony/15 flex items-center justify-center animate-consciousness-wave">
              <Zap className="w-8 h-8 text-consciousness" />
            </div>

            <Typography
              variant="3xl"
              weight="bold"
              font="display"
              className="mb-6 bg-gradient-to-r from-consciousness via-consciousness/90 to-harmony bg-clip-text text-transparent"
            >
              Shape the Future of Intelligence
            </Typography>
            <Typography
              variant="lg"
              color="secondary"
              className="mb-8 leading-relaxed max-w-3xl mx-auto"
            >
              Join leading enterprises in transforming their operations with adaptive AI systems.
              Experience the next generation of symbiotic intelligence today.
            </Typography>

            {/* Enhanced CTA buttons with better spacing and effects */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button
                variant="quantum"
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="group px-8 py-4 text-base font-semibold shadow-2xl shadow-consciousness/25 hover:shadow-consciousness/40 transition-all duration-500 hover:scale-105 rounded-[20px] relative overflow-hidden"
                onClick={() => window.location.href = '/get-started'}
              >
                <span className="relative z-10">Start Your Journey</span>
                <div className="absolute inset-0 bg-gradient-to-r from-consciousness/20 to-harmony/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
              </Button>
              <Button
                variant="outline-quantum"
                size="lg"
                className="group px-8 py-4 text-base font-semibold border-2 border-consciousness/30 hover:border-consciousness/60 transition-all duration-500 hover:scale-105 rounded-[20px] backdrop-blur-xl"
                onClick={() => window.location.href = '/enterprise-demo'}
              >
                <span className="group-hover:text-consciousness transition-colors duration-300">
                  Request Enterprise Demo
                </span>
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Main Footer Content */}
        <div className="grid lg:grid-cols-6 gap-12 mb-16">
          {/* Premium Brand Section */}
          <div className="lg:col-span-2">
            {/* Enhanced logo section with better visual hierarchy */}
            <div className="flex items-center space-x-4 mb-8 group">
              <div className="relative">
                <div className="absolute inset-0 bg-consciousness/25 rounded-full blur-lg animate-pulse"></div>
                <img
                  src="/lovable-uploads/69b2f8ba-ee9a-4a51-954a-923e57771f4d.png"
                  alt="SymbioWave"
                  className="w-16 h-16 drop-shadow-2xl relative z-10 group-hover:scale-105 transition-transform duration-500"
                />
              </div>
              <div>
                <Typography
                  variant="2xl"
                  weight="bold"
                  font="display"
                  className="mb-1 bg-gradient-to-r from-consciousness via-white to-harmony bg-clip-text text-transparent"
                >
                  SymbioWave
                </Typography>
                <Typography variant="xs" color="consciousness" className="uppercase tracking-widest font-semibold">
                  AI Intelligence Platform
                </Typography>
              </div>
            </div>

            {/* Enhanced description with better typography */}
            <Typography
              variant="base"
              color="secondary"
              className="mb-8 leading-relaxed text-white/80"
            >
              Leading the transformation of global commerce through adaptive,
              self-optimizing AI ecosystems. Trusted by Fortune 500 companies
              to deliver measurable business outcomes.
            </Typography>

            {/* Enhanced Contact Information */}
            <div className="mb-8">
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-4 flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                Enterprise Contact
              </Typography>
              <div className="space-y-3">
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness group-hover:shadow-lg group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness group-hover:shadow-lg group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness group-hover:shadow-lg group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
              </div>
            </div>

            {/* Enhanced Global Presence */}
            <div className="p-6 rounded-[20px] bg-gradient-to-br from-consciousness/8 via-consciousness/3 to-harmony/6 border border-consciousness/20 backdrop-blur-xl relative overflow-hidden group hover:border-consciousness/40 transition-all duration-500">
              {/* Subtle background pattern */}
              <div className="absolute inset-0 opacity-20">
                <div className="absolute top-2 right-2 w-8 h-8 bg-consciousness/20 rounded-full blur-md"></div>
                <div className="absolute bottom-2 left-2 w-6 h-6 bg-harmony/20 rounded-full blur-sm"></div>
              </div>

              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-3 flex items-center relative z-10">
                <MapPin className="w-4 h-4 mr-2" />
                Global Headquarters
              </Typography>
              <Typography variant="sm" color="secondary" className="leading-relaxed relative z-10 font-mono text-white/80">
                San Francisco • London • Singapore<br />
                Tokyo • Frankfurt • Sydney
              </Typography>
            </div>
          </div>

          {/* Premium Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-6">
              <Typography
                variant="base"
                weight="semibold"
                className="pb-3 border-b border-consciousness/25 bg-gradient-to-r from-consciousness to-harmony bg-clip-text text-transparent relative"
              >
                {section.title}
                <div className="absolute bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r from-consciousness to-harmony rounded-full"></div>
              </Typography>
              <ul className="space-y-4">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="group block transition-all duration-300 hover:translate-x-2 p-2 -m-2 rounded-lg hover:bg-consciousness/5"
                    >
                      <Typography
                        variant="sm"
                        className="group-hover:text-consciousness transition-colors font-medium text-white/80 group-hover:font-semibold"
                      >
                        {link.name}
                      </Typography>
                      <Typography
                        variant="xs"
                        className="mt-1 group-hover:text-white/70 transition-colors text-white/50 leading-relaxed"
                      >
                        {link.description}
                      </Typography>
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Enhanced Certifications & Compliance */}
        <div className="mb-12 p-8 rounded-[32px] bg-gradient-to-r from-consciousness/8 via-consciousness/3 to-harmony/8 border border-consciousness/20 backdrop-blur-xl relative overflow-hidden">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-4 left-4 w-16 h-16 bg-consciousness/15 rounded-full blur-xl"></div>
            <div className="absolute bottom-4 right-4 w-12 h-12 bg-harmony/15 rounded-full blur-lg"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-consciousness/10 rounded-full blur-2xl"></div>
          </div>

          <Typography variant="base" weight="semibold" className="mb-8 text-center bg-gradient-to-r from-consciousness to-harmony bg-clip-text text-transparent relative z-10">
            Enterprise Security & Compliance
          </Typography>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 relative z-10">
            {certifications.map((cert, index) => (
              <div key={index} className="flex flex-col items-center text-center group cursor-pointer">
                <div className="flex items-center justify-center w-14 h-14 rounded-[16px] bg-gradient-to-br from-consciousness/15 to-harmony/10 group-hover:from-consciousness/25 group-hover:to-harmony/20 transition-all duration-500 mb-4 border border-consciousness/20 group-hover:border-consciousness/40 group-hover:scale-110 backdrop-blur-sm">
                  <span className="text-consciousness group-hover:scale-110 transition-transform duration-300">{cert.icon}</span>
                </div>
                <Typography variant="xs" weight="semibold" className="mb-1 text-white/90 group-hover:text-consciousness transition-colors duration-300">
                  {cert.text}
                </Typography>
                <Typography variant="xs" className="text-white/60 group-hover:text-white/80 transition-colors duration-300">
                  {cert.subtext}
                </Typography>
              </div>
            ))}
          </div>
        </div>

        {/* Premium Bottom Bar */}
        <div className="border-t border-consciousness/25 pt-8 relative">
          {/* Subtle glow effect */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>

          <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-3 md:space-y-0 md:space-x-8">
              <Typography variant="sm" className="text-white/60 font-mono">
                © {currentYear} SymbioWave Corporation. All rights reserved.
              </Typography>
              <div className="flex flex-wrap justify-center md:justify-start gap-6 text-xs">
                <a href="/privacy" className="text-white/50 hover:text-consciousness transition-all duration-300 hover:scale-105 font-mono">Privacy Policy</a>
                <a href="/terms" className="text-white/50 hover:text-consciousness transition-all duration-300 hover:scale-105 font-mono">Terms of Service</a>
                <a href="/security" className="text-white/50 hover:text-consciousness transition-all duration-300 hover:scale-105 font-mono">Security</a>
                <a href="/accessibility" className="text-white/50 hover:text-consciousness transition-all duration-300 hover:scale-105 font-mono">Accessibility</a>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-6">
              <Typography variant="xs" className="flex items-center text-white/60 font-mono">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse shadow-lg shadow-green-400/50"></span>
                All Systems Operational
              </Typography>
              <Typography variant="xs" className="flex items-center text-white/60 font-mono">
                <Zap className="w-3 h-3 mr-1 text-consciousness animate-pulse" />
                Powered by ACI Technology
              </Typography>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;