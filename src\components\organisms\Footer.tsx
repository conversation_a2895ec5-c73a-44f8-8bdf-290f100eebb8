import React from 'react';
import { Mail } from 'lucide-react';
import Typography from '../atoms/Typography';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Ecosystem",
      links: [
        { name: "SymbioLabs", href: "/symbiolabs", description: "Research & Development" },
        { name: "SymbioImpact", href: "/symbioimpact", description: "ESG & Impact" },
        { name: "SymbioVentures", href: "/symbioventures", description: "Investment Platform" },
        { name: "SymbioAlliance", href: "/symbioalliance", description: "Partner Network" },
      ]
    },
    {
      title: "Solutions",
      links: [
        { name: "SymbioCore", href: "/symbiocore", description: "Core ACI Infrastructure" },
        { name: "SymbioXchange", href: "/symbioxchange", description: "ACI Marketplace" },
        { name: "SymbioAutomate", href: "/symbioautomate", description: "Process Automation" },
        { name: "SymbioEdge", href: "/symbioedge", description: "Edge Computing" },
      ]
    },
    {
      title: "Developers",
      links: [
        { name: "API Documentation", href: "/documentation", description: "Technical Guides" },
        { name: "SDK & Tools", href: "/sdk", description: "Development Kit" },
        { name: "Case Studies", href: "/case-studies", description: "Success Stories" },
        { name: "ACI Technology", href: "/aci-technology", description: "Core Technology" },
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About SymbioWave", href: "/about-us", description: "Our Story" },
        { name: "Leadership Team", href: "/leadership", description: "Executive Team" },
        { name: "Careers", href: "/careers", description: "Join Our Mission" },
        { name: "Investor Relations", href: "/investors", description: "Financial Info" },
      ]
    }
  ];



  return (
    <footer className="relative overflow-hidden bg-gradient-to-b from-abyssal-base via-abyssal-deep to-abyssal-void">
      {/* Clean Background */}
      <div className="absolute inset-0 opacity-20">
        {/* Simple gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-consciousness/2 via-transparent to-harmony/3"></div>
      </div>

      {/* Premium border gradient - Enhanced */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>
      <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-consciousness/20 to-transparent blur-sm"></div>

      <div className="container mx-auto px-8 py-8 relative z-10">

        {/* Enhanced Main Footer Content */}
        <div className="grid lg:grid-cols-6 gap-5 mb-5">
          {/* Premium Brand Section */}
          <div className="lg:col-span-2">
            {/* Enhanced logo section with professional alignment */}
            <div className="flex flex-col items-center justify-center mb-2 group">
              <div className="relative mb-2">
                <img
                  src="/lovable-uploads/69b2f8ba-ee9a-4a51-954a-923e57771f4d.png"
                  alt="SymbioWave"
                  className="w-40 h-40 drop-shadow-2xl relative z-10 group-hover:scale-105 transition-transform duration-500"
                />
              </div>
              <Typography variant="xs" color="consciousness" className="uppercase tracking-widest font-semibold mb-2 text-center leading-tight">
                Artificial Cellular Intelligence Platform
              </Typography>
            </div>

            {/* Enhanced description with better typography */}
            <Typography
              variant="sm"
              color="secondary"
              className="mb-2 leading-relaxed text-white/80 text-center"
            >
              Leading the transformation of global commerce through adaptive,
              self-optimizing ACI ecosystems.
            </Typography>

            {/* Enhanced Contact Information */}
            <div className="mb-2">
              <Typography variant="xs" weight="semibold" color="consciousness" className="mb-1 flex items-center justify-center">
                <Mail className="w-3 h-3 mr-2" />
                Enterprise Contact
              </Typography>
              <div className="space-y-1 flex flex-col items-center">
                <a href="mailto:<EMAIL>" className="group flex items-center text-xs text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-1.5 h-1.5 bg-consciousness/60 rounded-full mr-2 group-hover:bg-consciousness group-hover:shadow-sm group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-xs text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-1.5 h-1.5 bg-consciousness/60 rounded-full mr-2 group-hover:bg-consciousness group-hover:shadow-sm group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
              </div>
            </div>

          </div>

          {/* Premium Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-2 mt-6">
              <Typography
                variant="xs"
                weight="semibold"
                color="consciousness"
                className="pb-1 border-b border-consciousness/25 relative"
              >
                {section.title}
                <div className="absolute bottom-0 left-0 w-6 h-0.5 bg-gradient-to-r from-consciousness to-harmony rounded-full"></div>
              </Typography>
              <ul className="space-y-1">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="group block transition-all duration-300 hover:translate-x-1 py-0.5 -mx-1 rounded-lg hover:bg-consciousness/5"
                    >
                      <Typography
                        variant="xs"
                        className="group-hover:text-consciousness transition-colors font-medium text-white/80 group-hover:font-semibold"
                      >
                        {link.name}
                      </Typography>
                      <Typography
                        variant="xs"
                        className="group-hover:text-white/70 transition-colors text-white/50 leading-tight text-[10px]"
                      >
                        {link.description}
                      </Typography>
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>



        {/* Simple Bottom Bar */}
        <div className="border-t border-consciousness/25 pt-2 relative">
          {/* Subtle glow effect */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>

          <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
            <Typography variant="xs" className="text-white/60 font-mono">
              © {currentYear} SymbioWave Corporation. All rights reserved.
            </Typography>
            <Typography variant="xs" className="text-white/60 font-mono">
              Powered by ACI Technology
            </Typography>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;