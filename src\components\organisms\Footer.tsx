import React from 'react';
import { ArrowRight, Globe, Shield, Award, Zap } from 'lucide-react';
import Typography from '../atoms/Typography';
import Button from '../atoms/Button';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Platform",
      links: [
        { name: "SymbioCore", href: "/symbiocore", description: "Core AI Infrastructure" },
        { name: "SymbioLabs", href: "/symbiolabs", description: "Research & Development" },
        { name: "SymbioXchange", href: "/symbioxchange", description: "AI Marketplace" },
        { name: "SymbioAutomate", href: "/symbioautomate", description: "Process Automation" },
      ]
    },
    {
      title: "Solutions",
      links: [
        { name: "SymbioEdge", href: "/symbioedge", description: "Edge Computing" },
        { name: "SymbioImpact", href: "/symbioimpact", description: "ESG & Impact" },
        { name: "SymbioVentures", href: "/symbioventures", description: "Investment Platform" },
        { name: "SymbioAlliance", href: "/symbioalliance", description: "Partner Network" },
      ]
    },
    {
      title: "Developers",
      links: [
        { name: "API Documentation", href: "/documentation", description: "Technical Guides" },
        { name: "SDK & Tools", href: "/sdk", description: "Development Kit" },
        { name: "Case Studies", href: "/case-studies", description: "Success Stories" },
        { name: "ACI Technology", href: "/aci-technology", description: "Core Technology" },
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About SymbioWave", href: "/about-us", description: "Our Story" },
        { name: "Leadership Team", href: "/leadership", description: "Executive Team" },
        { name: "Careers", href: "/careers", description: "Join Our Mission" },
        { name: "Investor Relations", href: "/investors", description: "Financial Info" },
      ]
    }
  ];

  const certifications = [
    { icon: <Shield className="w-5 h-5" />, text: "SOC 2 Type II", subtext: "Security Certified" },
    { icon: <Award className="w-5 h-5" />, text: "ISO 27001", subtext: "Information Security" },
    { icon: <Globe className="w-5 h-5" />, text: "GDPR Compliant", subtext: "Data Protection" },
    { icon: <Zap className="w-5 h-5" />, text: "Carbon Neutral", subtext: "Environmental Impact" }
  ];

  return (
    <footer className="relative overflow-hidden bg-gradient-to-b from-background via-background/95 to-background/90">
      {/* Sophisticated Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-consciousness/3 via-transparent to-harmony/5"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-consciousness/8 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/3 right-1/3 w-64 h-64 bg-harmony/6 rounded-full blur-2xl"></div>
          <div className="absolute top-1/2 right-1/4 w-32 h-32 bg-consciousness/4 rounded-full blur-xl"></div>
        </div>
        {/* Subtle grid pattern */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
          backgroundSize: '48px 48px'
        }}></div>
      </div>

      {/* Premium border gradient */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/30 to-transparent"></div>

      <div className="container mx-auto px-8 py-24 relative z-10">
        {/* Strategic CTA Section */}
        <div className="mb-20 text-center">
          <div className="max-w-4xl mx-auto">
            <Typography 
              variant="3xl" 
              weight="bold" 
              font="display"
              color="consciousness"
              className="mb-6 bg-gradient-to-r from-consciousness via-consciousness/90 to-harmony bg-clip-text text-transparent"
            >
              Shape the Future of Intelligence
            </Typography>
            <Typography 
              variant="lg" 
              color="secondary"
              className="mb-8 leading-relaxed"
            >
              Join leading enterprises in transforming their operations with adaptive AI systems. 
              Experience the next generation of symbiotic intelligence today.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="primary-quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="px-8 py-4 text-base font-semibold shadow-2xl shadow-consciousness/20"
                onClick={() => window.location.href = '/get-started'}
              >
                Start Your Journey
              </Button>
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="px-8 py-4 text-base font-semibold border-2"
                onClick={() => window.location.href = '/enterprise-demo'}
              >
                Request Enterprise Demo
              </Button>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="grid lg:grid-cols-6 gap-12 mb-16">
          {/* Enhanced Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-4 mb-8">
              <div className="relative">
                <img 
                  src="/lovable-uploads/69b2f8ba-ee9a-4a51-954a-923e57771f4d.png" 
                  alt="SymbioWave" 
                  className="w-14 h-14 drop-shadow-lg"
                />
                <div className="absolute inset-0 bg-consciousness/20 rounded-full blur-md -z-10"></div>
              </div>
              <div>
                <Typography 
                  variant="2xl" 
                  weight="bold" 
                  font="display"
                  color="consciousness"
                  className="mb-1"
                >
                  SymbioWave
                </Typography>
                <Typography variant="xs" color="tertiary" className="uppercase tracking-wider">
                  AI Intelligence Platform
                </Typography>
              </div>
            </div>

            <Typography 
              variant="base" 
              color="secondary"
              className="mb-8 leading-relaxed"
            >
              Leading the transformation of global commerce through adaptive, 
              self-optimizing AI ecosystems. Trusted by Fortune 500 companies 
              to deliver measurable business outcomes.
            </Typography>

            {/* Contact Information */}
            <div className="mb-8">
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-4">
                Enterprise Contact
              </Typography>
              <div className="space-y-3">
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-tertiary hover:text-consciousness transition-all duration-300">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness transition-colors"></span>
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-tertiary hover:text-consciousness transition-all duration-300">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness transition-colors"></span>
                  <EMAIL>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-sm text-tertiary hover:text-consciousness transition-all duration-300">
                  <span className="w-2 h-2 bg-consciousness/60 rounded-full mr-3 group-hover:bg-consciousness transition-colors"></span>
                  <EMAIL>
                </a>
              </div>
            </div>

            {/* Global Presence */}
            <div className="p-6 rounded-2xl bg-gradient-to-br from-consciousness/5 to-harmony/5 border border-consciousness/10">
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-3">
                Global Headquarters
              </Typography>
              <Typography variant="sm" color="tertiary" className="leading-relaxed">
                San Francisco • London • Singapore<br />
                Tokyo • Frankfurt • Sydney
              </Typography>
            </div>
          </div>

          {/* Enhanced Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-6">
              <Typography 
                variant="base" 
                weight="semibold" 
                color="consciousness"
                className="pb-2 border-b border-consciousness/20"
              >
                {section.title}
              </Typography>
              <ul className="space-y-4">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a 
                      href={link.href}
                      className="group block transition-all duration-300 hover:translate-x-1"
                    >
                      <Typography 
                        variant="sm" 
                        color="secondary"
                        className="group-hover:text-consciousness transition-colors font-medium"
                      >
                        {link.name}
                      </Typography>
                      <Typography 
                        variant="xs" 
                        color="quaternary"
                        className="mt-1 group-hover:text-tertiary transition-colors"
                      >
                        {link.description}
                      </Typography>
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Certifications & Compliance */}
        <div className="mb-12 p-8 rounded-3xl bg-gradient-to-r from-consciousness/5 via-transparent to-harmony/5 border border-consciousness/10">
          <Typography variant="sm" weight="semibold" color="consciousness" className="mb-6 text-center">
            Enterprise Security & Compliance
          </Typography>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <div key={index} className="flex flex-col items-center text-center group">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-consciousness/10 group-hover:bg-consciousness/20 transition-colors duration-300 mb-3">
                  <span className="text-consciousness">{cert.icon}</span>
                </div>
                <Typography variant="xs" weight="semibold" color="secondary" className="mb-1">
                  {cert.text}
                </Typography>
                <Typography variant="xs" color="quaternary">
                  {cert.subtext}
                </Typography>
              </div>
            ))}
          </div>
        </div>

        {/* Enhanced Bottom Bar */}
        <div className="border-t border-consciousness/20 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-8">
              <Typography variant="sm" color="quaternary">
                © {currentYear} SymbioWave Corporation. All rights reserved.
              </Typography>
              <div className="flex space-x-6 text-xs">
                <a href="/privacy" className="text-quaternary hover:text-consciousness transition-colors">Privacy Policy</a>
                <a href="/terms" className="text-quaternary hover:text-consciousness transition-colors">Terms of Service</a>
                <a href="/security" className="text-quaternary hover:text-consciousness transition-colors">Security</a>
                <a href="/accessibility" className="text-quaternary hover:text-consciousness transition-colors">Accessibility</a>
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              <Typography variant="xs" color="quaternary" className="flex items-center">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                All Systems Operational
              </Typography>
              <Typography variant="xs" color="quaternary" className="flex items-center">
                <Zap className="w-3 h-3 mr-1 text-consciousness" />
                Powered by ACI Technology
              </Typography>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;